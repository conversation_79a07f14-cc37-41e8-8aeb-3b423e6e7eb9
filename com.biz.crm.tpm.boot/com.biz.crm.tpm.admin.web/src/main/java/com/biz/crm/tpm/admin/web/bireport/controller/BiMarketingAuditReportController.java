package com.biz.crm.tpm.admin.web.bireport.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.bireport.service.BiMarketingAuditReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/biMarketingAuditReportController")
@Slf4j
@Api(tags = "Bi结案明细报表")
public class BiMarketingAuditReportController {

    @Autowired(required = false)
    BiMarketingAuditReportService biMarketingAuditReportService;

    @GetMapping("runBiMarketingAuditReport")
    @ApiOperation(value = "跑Bi结案明细报表")
    public Result runBiMarketingAuditReport(@RequestParam(required = false) String years,  @RequestParam(required = false,defaultValue = "Y") String initFlag){
        List<String> yearsList = StringUtils.isNotBlank(years) ? Arrays.asList(years) : null;
        biMarketingAuditReportService.removeAll(yearsList);
        biMarketingAuditReportService.calMarketingAuditReportReport(yearsList,initFlag);
        return Result.ok();
    }

    @ApiOperation(value = "定时任务执行Bi结案明细报表")
    @GetMapping("scheduleBiMarketingAuditReport")
    public Result scheduleBiMarketingAuditReport(){
        List<String> yearsList = Arrays.asList(LocalDate.now().minusMonths(1).toString(), LocalDate.now().toString());
        biMarketingAuditReportService.removeAll(yearsList);
        biMarketingAuditReportService.calMarketingAuditReportReport(yearsList,"N");
        return Result.ok();
    }
}
