package com.biz.crm.tpm.admin.web.service.internal;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.biz.crm.business.common.ie.local.BusinessExcelExportTemplateWriteUtil;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.business.common.sdk.service.RedisService;
import com.biz.crm.mdm.business.customer.sdk.service.CustomerVoService;
import com.biz.crm.mdm.business.customer.sdk.vo.CustomerVo;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.mdm.business.org.sdk.service.OrgVoService;
import com.biz.crm.mdm.business.org.sdk.vo.OrgVo;
import com.biz.crm.tpm.admin.web.exports.withholdingcollect.*;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanCaseService;
import com.biz.crm.tpm.business.activities.marketingplan.service.MarketingPlanService;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import com.biz.crm.tpm.business.activities.regioncollect.service.RegionCollectService;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectDepartmentEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectGainsAndLossesVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectItemEstimationVo;
import com.biz.crm.tpm.business.activities.regioncollect.vo.RegionCollectMarketingEstimationVo;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingCollect;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.local.repository.WithHoldingCollectRepository;
import com.biz.crm.tpm.business.pay.local.service.WithHoldingService;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.enums.WithholdingReportTypeEnum;
import com.biz.crm.tpm.business.pay.sdk.service.WithHoldingCollectService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import com.biz.crm.tpm.business.pay.sdk.vo.WithHoldingVo;
import com.bizunited.nebula.common.service.NebulaToolkitService;
import com.google.common.collect.Maps;
import liquibase.pro.packaged.M;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/6/26 18:06
 */
@Service
public class WithholdingCollectExportServiceImpl {

    @Resource
    private BusinessExcelExportTemplateWriteUtil writeUtil;

    @Resource
    private RegionCollectService regionCollectService;

    @Resource
    private NebulaToolkitService nebulaToolkitService;

    @Autowired(required = false)
    private MarketingPlanCaseService marketingPlanCaseService;
    @Autowired(required = false)
    private MarketingPlanService marketingPlanService;

    @Resource
    private LoginUserService loginUserService;

    @Resource
    private DictDataVoService dictDataVoService;

    @Resource
    private WithHoldingCollectRepository withHoldingCollectRepository;

    @Autowired(required = false)
    private RedisService redisService;

    @Autowired
    private CustomerVoService customerVoService;

    @Autowired
    private WithHoldingCollectService withHoldingCollectService;

    @Autowired
    private OrgVoService orgVoService;

    @Autowired
    private FeeCashDetailRepository feeCashDetailRepository;

    private static final String WITHHOLDING_TYPE = "withholding_type";


    private final static ThreadLocal<Map<String, String>> withholdingTypeThreadLocal = new ThreadLocal<>();


    /**
     * 计提类型
     *
     * @return
     */
    private Map<String, String> withholdingTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(WITHHOLDING_TYPE);
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    private Map<String, String> auditStatusMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("audit_status");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    private Map<String, String> cashStatusMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("cash_status");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    private Map<String, String> caseTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("TPM_SCHEME_CASE_TYPE");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }


    private Map<String, String> cooperateTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("mdm_cooperate_type");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    private Map<String, String> bmpStatusMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("mdm_bpm_status");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }


    private Map<String, String> cashMethodMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("tpm_scheme_cash_type");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    private Map<String, String> cashTypeMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("fee_cash_type");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }


    private Map<String, String> payStatusMap() {
        List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode("tpm_pay_status");
        return dictDataVos.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
    }

    /**
     * 导出大区汇总数据
     *
     * @param code,cacheKey
     * @return
     */
    public String exportWithholdingCollect(String code, String cacheKey) {
        //模板文件地址
        String templateFilePath = "exceltemplate/WithholdingCollectReport.xlsx";
        WithHoldingCollect entity = withHoldingCollectRepository.findByCode(code);
        Validate.isTrue(ObjectUtils.isNotEmpty(entity), "计提汇总数据不存在");
        //新文件名称
        String outputFileName = String.format("计提%s汇总", entity.getOrgName());
        Map<String, List<?>> outputMap = Maps.newHashMap();
        Map<String, String> withholdingTypeMap = withholdingTypeMap();
        Map<String, String> auditStatusMap = auditStatusMap();
        Map<String, String> cashStatusMap = cashStatusMap();
        Map<String, String> caseTypeMap = caseTypeMap();
        Map<String, String> cooperateTypeMap = cooperateTypeMap();
        Map<String, String> bmpStatusMap = bmpStatusMap();
        Map<String, String> cashMethodMap = cashMethodMap();
        Map<String, String> cashTypeMap = cashTypeMap();
        Map<String, String> payStatusMap = payStatusMap();


        List<RegionCollectMarketingEstimationVo> marketingEstimationVos = (List<RegionCollectMarketingEstimationVo>) redisService.get(cacheKey + ":" + WithholdingReportTypeEnum.MARKETING_ESTIMATION);
        List<RegionCollectGainsAndLossesVo> gainsAndLossesVoList = (List<RegionCollectGainsAndLossesVo>) redisService.get(cacheKey + ":" + WithholdingReportTypeEnum.CUSTOMER_GAINS_LOSSES);
        List<RegionCollectDepartmentEstimationVo> departmentEstimationVoList = (List<RegionCollectDepartmentEstimationVo>) redisService.get(cacheKey + ":" + WithholdingReportTypeEnum.DEPARTMENT_ESTIMATION);
        List<RegionCollectItemEstimationVo> itemEstimationVoList = (List<RegionCollectItemEstimationVo>) redisService.get(cacheKey + ":" + WithholdingReportTypeEnum.PRODUCT_PHASE_ESTIMATION);
        List<WithHoldingVo> withHoldingVos = (List<WithHoldingVo>) redisService.get(cacheKey + ":" + WithholdingReportTypeEnum.WITHHOLDING_DETAIL);

        //营销测算
        if (CollectionUtils.isNotEmpty(marketingEstimationVos)) {
            List<WithholdingCollectMarketingEstimationExportVo> dataList = (List<WithholdingCollectMarketingEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(marketingEstimationVos,
                    RegionCollectMarketingEstimationVo.class, WithholdingCollectMarketingEstimationExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("营销测算表", dataList);
        }
        //三级部门测算
        if (CollectionUtils.isNotEmpty(departmentEstimationVoList)) {
            List<WithholdingCollectDepartmentEstimationExportVo> dataList = (List<WithholdingCollectDepartmentEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(departmentEstimationVoList,
                    RegionCollectDepartmentEstimationVo.class, WithholdingCollectDepartmentEstimationExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("三级部门测算表", dataList);
        }
        //客户损益预测
        if (CollectionUtils.isNotEmpty(gainsAndLossesVoList)) {
            assembleChannelType(gainsAndLossesVoList);
            List<WithholdingCollectGainsAndLossesExportVo> dataList = (List<WithholdingCollectGainsAndLossesExportVo>) nebulaToolkitService.copyCollectionByWhiteList(gainsAndLossesVoList,
                    RegionCollectGainsAndLossesVo.class, WithholdingCollectGainsAndLossesExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("客户损益表", dataList);
        }
        //品项分析
        if (CollectionUtils.isNotEmpty(itemEstimationVoList)) {
            List<WithholdingCollectItemEstimationExportVo> dataList = (List<WithholdingCollectItemEstimationExportVo>) nebulaToolkitService.copyCollectionByWhiteList(itemEstimationVoList, RegionCollectItemEstimationVo.class,
                    WithholdingCollectItemEstimationExportVo.class, HashSet.class, ArrayList.class);
            outputMap.put("品项分析表", dataList);
        }
        //计提明细
        if (CollectionUtils.isNotEmpty(withHoldingVos)) {
            List<WithholdingCollectDetailExportVo> dataList = (List<WithholdingCollectDetailExportVo>) nebulaToolkitService.copyCollectionByWhiteList(withHoldingVos,
                    WithHoldingVo.class, WithholdingCollectDetailExportVo.class, HashSet.class, ArrayList.class);
            for (WithholdingCollectDetailExportVo vo : dataList) {
                if (ObjectUtils.isNotEmpty(vo.getWithHoldingType())) {
                    Map<String, String> map = withholdingTypeMap;
                    String withholdingType = map.get(vo.getWithHoldingType());
                    vo.setWithHoldingType(withholdingType);
                }
            }
            outputMap.put("计提明细表", dataList);
        }
        // 搭赠及周边
        String orgCode = entity.getDepartmentCode();
        String yearMonthLy = entity.getYearMonthLy();
        List<OrgVo> orgChildrenVos = orgVoService.findAllChildrenByOrgCode(orgCode);
        List<String> orgCodeList = orgChildrenVos.stream().map(OrgVo::getOrgCode).collect(Collectors.toList());

        Pageable pageable =  PageRequest.of(1, 50000);
        List<DictDataVo> giftSurroundExpenseItem = dictDataVoService.findByDictTypeCode("gift_surround_expense_item");
        List<String> itemCodes = giftSurroundExpenseItem.stream().map(DictDataVo::getDictCode).collect(Collectors.toList());
        MarketingPlanCaseVo planCaseVo = new MarketingPlanCaseVo();
        planCaseVo.setYears(yearMonthLy);
        planCaseVo.setOrgCodeList(orgCodeList);
        planCaseVo.setDetailCodeList(itemCodes);
        Page<MarketingPlanCaseVo> voPage = marketingPlanCaseService.findMarketingPlanCaseReportList(pageable, planCaseVo);
        if (CollectionUtils.isNotEmpty(voPage.getRecords())) {
            List<WithholdingCollectGiftAndSurroundExportVo> dataList = (List<WithholdingCollectGiftAndSurroundExportVo>) nebulaToolkitService.copyCollectionByWhiteList(voPage.getRecords(),
                    MarketingPlanCaseVo.class, WithholdingCollectGiftAndSurroundExportVo.class, HashSet.class, ArrayList.class);
            List<String> belongDepartmentCodes = dataList.stream().map(WithholdingCollectGiftAndSurroundExportVo::getBelongDepartmentCode).distinct().collect(Collectors.toList());
            Map<String, List<OrgVo>> allParentByOrgCodesMap = orgVoService.findAllParentByOrgCodesMap(belongDepartmentCodes);
            dataList.forEach(e -> {
                if (allParentByOrgCodesMap.containsKey(e.getBelongDepartmentCode())) {
                    List<OrgVo> orgVos = allParentByOrgCodesMap.get(e.getBelongDepartmentCode());
                    List<OrgVo> orgVoFilter = orgVos.stream().filter(m -> m.getLevelNum().equals(2)).collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(orgVoFilter)) {
                        e.setDepartmentOneCode(orgVoFilter.get(0).getOrgCode());
                        e.setDepartmentOneName(orgVoFilter.get(0).getOrgName());
                    }
                }
                e.setCaseType(caseTypeMap.get(e.getCaseType()));
                e.setAuditStatus(auditStatusMap.get(e.getAuditStatus()));
                e.setCashStatus(cashStatusMap.get(e.getCashStatus()));
                e.setHzlx(cooperateTypeMap.get(e.getHzlx()));
            });
            outputMap.put("搭赠及周边", dataList);
        }

        Pageable pageable2 =  PageRequest.of(1, 50000);
        // 已结案兑付
        FeeCashDetailDto feeCashDetailDto = new FeeCashDetailDto();
        feeCashDetailDto.setYears(yearMonthLy);
        feeCashDetailDto.setBelongDepartmentCodes(orgCodeList);
        feeCashDetailDto.setBeFullAudit("Y");
        Page<FeeCashDetailVo> page = feeCashDetailRepository.findFullAuditedDetailList(pageable2, feeCashDetailDto);
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            List<WithholdingCollectAuditedAndCashedExportVo> dataList = (List<WithholdingCollectAuditedAndCashedExportVo>) nebulaToolkitService.copyCollectionByWhiteList(page.getRecords(),
                    FeeCashDetailVo.class, WithholdingCollectAuditedAndCashedExportVo.class, HashSet.class, ArrayList.class);
            dataList.forEach(e -> {
                e.setStatus(bmpStatusMap.get(e.getStatus()));
                e.setCashMethod(cashMethodMap.get(e.getCashMethod()));
                e.setBeCash("N".equals(e.getBeCash())?"否":"是");
                e.setCashType(cashTypeMap.get(e.getCashType()));
                e.setPayStatus(payStatusMap.get(e.getPayStatus()));
                e.setCooperateType(cooperateTypeMap.get(e.getCooperateType()));
            });
            outputMap.put("已结案兑付活动", dataList);
        }


        String fileCode = writeUtil.exportExcelByTemplate(templateFilePath, outputFileName, outputMap, null);

        return fileCode;
    }


    private void assembleChannelType(List<RegionCollectGainsAndLossesVo> gainsAndLossesList) {
        List<String> customerCodes = gainsAndLossesList.stream().map(RegionCollectGainsAndLossesVo::getCustomerCode).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<CustomerVo> customerVos = customerVoService.findByCustomerCodes(customerCodes);
        Map<String, String> customerMap = customerVos.stream().filter(e -> StringUtils.isNotEmpty(e.getCustomerCode()) && Objects.nonNull(e.getChannelType()))
                .collect(Collectors.toMap(CustomerVo::getCustomerCode, CustomerVo::getChannelType, (v1,v2) -> v1));
        List<DictDataVo> channelTypeList = dictDataVoService.findByDictTypeCode("channel_type");
        Map<String, String> channelTypeMap = channelTypeList.stream().collect(Collectors.toMap(DictDataVo::getDictCode, DictDataVo::getDictValue));
        gainsAndLossesList.forEach(e -> {
            String customerCode = e.getCustomerCode();
            String channelTypeStr = customerMap.get(customerCode);
            if (StringUtils.isNotEmpty(channelTypeStr)) {
                List<String> channleTypeList = Arrays.asList(channelTypeStr.split(","));
                e.setChannelTypeList(channleTypeList);
                // 这里字段命名不严谨 先这么处理
                e.setChannelType(channleTypeList.stream().map(channelType -> channelTypeMap.getOrDefault(channelType," 缺省（未配置）")).collect(Collectors.joining(",")));
            }
        });
    }

}
