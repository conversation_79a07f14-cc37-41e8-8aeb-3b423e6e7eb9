package com.biz.crm.tpm.admin.web.bireport.controller;

import com.biz.crm.business.common.sdk.model.Result;
import com.biz.crm.tpm.admin.web.bireport.service.BiMarketingPlanCaseReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/biMarketingPlanCaseReportController")
@Slf4j
@Api(tags = "Bi活动明细报表")
public class BiMarketingPlanCaseReportController {

    @Autowired
    BiMarketingPlanCaseReportService biMarketingPlanCaseReportService;

    @GetMapping("runBiMarketingPlanCaseReport")
    @ApiOperation(value = "跑Bi活动明细报表")
    public Result runBiMarketingPlanCaseReport(@RequestParam(required = false) String startDate,@RequestParam(required = false)  String endDate,@RequestParam(required = false) String initFlag){
        biMarketingPlanCaseReportService.removeAll(null,null);
        biMarketingPlanCaseReportService.caliMarketingPlanCaseReport(startDate,endDate,initFlag);
        return Result.ok();
    }

    @ApiOperation(value = "定时任务执行Bi活动明细报表")
    @GetMapping("scheduleBiMarketingPlanCaseReport")
    public Result scheduleBiMarketingPlanCaseReport(){
        String startDate = LocalDate.now().minusDays(7).toString().concat(" 00:00:00").toString();
        String endDate = LocalDate.now().toString().concat(" 23:59:59");
        log.info("BI活动明细报表落 startDate {}  endDate {}",startDate,endDate);
        biMarketingPlanCaseReportService.removeAll(startDate,endDate);
        biMarketingPlanCaseReportService.caliMarketingPlanCaseReport(startDate,endDate,"N");
        return Result.ok();
    }


}
