package com.biz.crm.tpm.admin.web.bireport.service.internal;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.biz.crm.business.common.sdk.service.LoginUserService;
import com.biz.crm.tpm.admin.web.bireport.entity.BiMarketingPlanCaseReportEntity;
import com.biz.crm.tpm.admin.web.bireport.mapper.BiMarketingPlanCaseReportMapper;
import com.biz.crm.tpm.admin.web.bireport.service.BiMarketingPlanCaseReportService;
import com.biz.crm.tpm.admin.web.service.internal.MarketingPlanReportServiceImpl;
import com.biz.crm.tpm.business.activities.marketingplan.vo.MarketingPlanCaseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * BI活动明细报表
 * <AUTHOR>
 */
@Slf4j
@Service
public class BiMarketingPlanCaseReportServiceImpl extends ServiceImpl<BiMarketingPlanCaseReportMapper,BiMarketingPlanCaseReportEntity> implements BiMarketingPlanCaseReportService {

    @Autowired(required = false)
    private MarketingPlanReportServiceImpl marketingPlanReportService;

    @Autowired(required = false)
    private LoginUserService loginUserService;

    @Value("${spring.profiles.active:}")
    private String env;

    @Async("tpmMarketingCheckCaseThread")
    @Override
    public void caliMarketingPlanCaseReport(String startDate, String endDate,String initFlag) {

        if("N".equals(initFlag) && (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate))){
            log.error("BI活动明细报表落库非首次初始化，未传递活动开始时间、活动结束时间");
            return;
        }
        loginUserService.refreshAuthentication(null);
        StopWatch st = new StopWatch();
        st.start("BI活动明细报表落库任务");
        MarketingPlanCaseVo vo = new MarketingPlanCaseVo();
        vo.setStartDate(startDate);
        vo.setEndDate(endDate);
        int searchIndex = 1;
        int total = 0;
        long originTotal = 0;
        while (true){
            if("dev".equals(env)){
                log.info("BI活动明细报表落库查询1 searchIndex {} ",searchIndex);
            }
            Page<MarketingPlanCaseVo> page = marketingPlanReportService.findMarketingPlanCaseReportList(PageRequest.of(searchIndex++, 1000), vo);
            List<MarketingPlanCaseVo> records = page.getRecords();
            if(CollectionUtils.isEmpty(records)){
                log.info("BI活动明细报表落库查询总数 searchIndex : {}  recordsTotalSize : {} originTotal : {}  ",searchIndex,total,originTotal);
                break;
            }
            if(originTotal == 0){
                originTotal = page.getTotal();
            }
            adjustData(records);
            total+=records.size();
            List<BiMarketingPlanCaseReportEntity> list = JSONObject.parseArray(JSONObject.toJSONString(records), BiMarketingPlanCaseReportEntity.class);
            for (BiMarketingPlanCaseReportEntity biMarketingPlanCaseReportEntity : list) {
                biMarketingPlanCaseReportEntity.setId(null);
            }
            saveBatch(list);
            list = null;
            page = null;

        }
        st.stop();
        log.info("BI活动明细报表落库执行耗时  totalTimeSeconds {} ",st.getTotalTimeSeconds());
    }

    @Override
    public void removeAll(String startDate,String endDate) {

        this.getBaseMapper()
                .delete(new LambdaQueryWrapper<BiMarketingPlanCaseReportEntity>()
                .ge(StringUtils.isNotBlank(startDate),BiMarketingPlanCaseReportEntity::getStartDate,startDate)
                .le(StringUtils.isNotBlank(endDate),BiMarketingPlanCaseReportEntity::getEndDate,endDate));
    }

    private void adjustData(List<MarketingPlanCaseVo> records) {
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        for (MarketingPlanCaseVo model : records) {
            model.setDetailCodeList(null);
            model.setChannelTypeList(null);
            model.setOrgCodeList(null);
            model.setBearDepartmentCodes(null);
            model.setCashTypeSet(null);
            model.setBelongDepartmentCodes(null);
            model.setCustomerCodes(null);
            model.setSubjectCodes(null);
            model.setSchemeDetailCodeList(null);
            model.setSubjectCodes(null);
            model.setApprovalList(null);
            model.setCollectList(null);
            model.setYearsSet(null);
            model.setPayBys(null);
            model.setCollectList(null);
            model.setFeeLevelList(null);
            model.setFeeBelongItemList(null);
            model.setFeeItemList(null);
            model.setFeeProductList(null);
            model.setItemList(null);
            model.setProductAndItemList(null);
        }
    }
}
