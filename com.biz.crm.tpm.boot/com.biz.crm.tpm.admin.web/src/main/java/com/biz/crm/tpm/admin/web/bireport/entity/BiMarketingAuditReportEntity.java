package com.biz.crm.tpm.admin.web.bireport.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_bi_marketing_audit_report")
@Table(name = "tpm_bi_marketing_audit_report")
@org.hibernate.annotations.Table(appliesTo = "tpm_bi_marketing_audit_report", comment = "BI结案明细报表")
@ApiModel(value = "BiMarketingPlanCaseReportEntity", description = "BI结案明细报表")
public class BiMarketingAuditReportEntity   extends TenantFlagOpEntity {

    @ApiModelProperty("审批状态")
    @Column(name = "status", columnDefinition = "varchar(255) comment '审批状态'")
    private String schemeDetailCode;

    @ApiModelProperty("方案结案编码")
    @Column(name = "audit_code", columnDefinition = "varchar(255) comment '方案结案编码'")
    private String auditCode;

    @ApiModelProperty("结案明细编码")
    @Column(name = "audit_detail_code", columnDefinition = "varchar(255) comment '结案明细编码'")
    private String auditDetailCode;

    @ApiModelProperty("方案结案名称 ")
    @Column(name = "audit_name", columnDefinition = "varchar(255) comment '方案结案名称 '")
    private String auditName;

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(255) comment '方案编码'")
    private String scheme_code;

    @ApiModelProperty("方案名称")
    @Column(name = "scheme_name", columnDefinition = "varchar(255) comment '方案名称'")
    private String schemeName;

    @ApiModelProperty("费用项目名称")
    @Column(name = "detail_name", columnDefinition = "varchar(255) comment '费用项目名称'")
    private String detail_name;

    @ApiModelProperty("公司编码")
    @Column(name = "company_code", columnDefinition = "varchar(255) comment '公司编码'")
    private String companyCode;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(255) comment '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(255) comment '结束时间'")
    private String endDate;

    @ApiModelProperty("年月")
    @Column(name = "years", columnDefinition = "varchar(255) comment '年月'")
    private String years;

    @ApiModelProperty("使用部门编码")
    @Column(name = "belong_department_code", columnDefinition = "varchar(255) comment '使用部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("使用部门名称")
    @Column(name = "belong_department_name", columnDefinition = "varchar(255) comment '使用部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    @Column(name = "bear_department_code", columnDefinition = "varchar(255) comment '承担部门编码'")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @Column(name = "bear_department_name", columnDefinition = "varchar(255) comment '承担部门名称'")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(255) comment '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @Column(name = "cost_center_name", columnDefinition = "varchar(255) comment '成本中心名称'")
    private String costCenterName;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(255) comment '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(255) comment '客户名称'")
    private String customerName;

    @ApiModelProperty("合作类型")
    @Column(name = "hzlx", columnDefinition = "varchar(255) comment '合作类型'")
    private String hzlx;

    @ApiModelProperty("门店编码")
    @Column(name = "terminal_code", columnDefinition = "varchar(255) comment '门店编码'")
    private String terminalCode;

    @ApiModelProperty("门店名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(255) comment '门店名称'")
    private String terminalName;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", columnDefinition = "varchar(255) comment '品项名称'")
    private String itemName;

    @ApiModelProperty("兑付方式")
    @Column(name = "cash_type", columnDefinition = "varchar(255) comment '兑付方式'")
    private String cashType;

    @ApiModelProperty("执行描述")
    @Column(name = "execute_desc", columnDefinition = "varchar(1000) comment '执行描述'")
    private String executeDesc;

    @ApiModelProperty("申请金额")
    @Column(name = "apply_amount", columnDefinition = "varchar(255) comment '申请金额'")
    private String applyAmount;

    @ApiModelProperty("费用依据")
    @Column(name = "cost_basis", columnDefinition = "varchar(255) comment '费用依据'")
    private String costBasis;

    @ApiModelProperty("本次结案金额 ")
    @Column(name = "audit_amount", columnDefinition = "varchar(255) comment '本次结案金额 '")
    private String auditAmount;

    @ApiModelProperty("是否完全结案")
    @Column(name = "be_full_audit", columnDefinition = "varchar(255) comment '是否完全结案'")
    private String beFullAudit;

    @ApiModelProperty("预付金额")
    @Column(name = "prepay_amount", columnDefinition = "varchar(255) comment '预付金额'")
    private String prepayAmount;

    @ApiModelProperty("预付核销余额")
    @Column(name = "available_reversed_amount", columnDefinition = "varchar(255) comment '预付核销余额'")
    private String availableReversedAmount;

    @ApiModelProperty("核销金额 ")
    @Column(name = "cash_amount", columnDefinition = "varchar(255) comment '核销金额 '")
    private String cashAmount;

    @ApiModelProperty("关闭金额")
    @Column(name = "close_amount", columnDefinition = "varchar(255) comment '关闭金额'")
    private String closeAmount;

    @ApiModelProperty("核销余额")
    @Column(name = "cash_balance", columnDefinition = "varchar(255) comment '核销余额'")
    private String cashBalance;

    @ApiModelProperty("兑付余额")
    @Column(name = "available_cash_prepay_amount", columnDefinition = "varchar(255) comment '兑付余额'")
    private String availableCashPrepayAmount;

    @ApiModelProperty("结案完结日期")
    @Column(name = "audit_date", columnDefinition = "varchar(255) comment '结案完结日期'")
    private String auditDate;

    @ApiModelProperty("票据类型")
    @Column(name = "bill_type", columnDefinition = "varchar(255) comment '票据类型'")
    private String billType;

    @ApiModelProperty("超节点时长")
    @Column(name = "over_day", columnDefinition = "varchar(255) comment '超节点时长'")
    private String overDay;

    @ApiModelProperty("上账状态 ")
    @Column(name = "account_status", columnDefinition = "varchar(255) comment '上账状态'")
    private String accountStatus;

    @ApiModelProperty("是否完全兑付 ")
    @Column(name = "be_full_cash", columnDefinition = "varchar(255) comment '是否完全兑付'")
    private String beFullCash;

    @ApiModelProperty("自动兑付编码")
    @Column(name = "auto_cash_code", columnDefinition = "varchar(255) comment '自动兑付编码'")
    private String autoCashCode;

    @ApiModelProperty("一级部门编码")
    @Column(name = "department_one_code", columnDefinition = "varchar(255) comment '一级部门编码'")
    private String departmentOneCode;

    @ApiModelProperty("一级部门名称")
    @Column(name = "department_one_name", columnDefinition = "varchar(255) comment '一级部门名称'")
    private String departmentOneName;

    @ApiModelProperty("创建人账号")
    @Column(name = "create_account", columnDefinition = "varchar(255) comment '创建人账号'")
    private String createAccount;

    @ApiModelProperty("创建人名称")
    @Column(name = "create_name", columnDefinition = "varchar(255) comment '创建人名称'")
    private String createName;

    @ApiModelProperty("活动描述")
    @Column(name = "act_desc", columnDefinition = "varchar(255) comment '活动描述'")
    private String actDesc;

    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(255) comment '活动执行编码'")
    private String actExecuteCode;

    @ApiModelProperty("组织编码")
    @Column(name = "org_code", columnDefinition = "varchar(255) comment '组织编码'")
    private String orgCode;

    @ApiModelProperty("组织名称")
    @Column(name = "org_name", columnDefinition = "varchar(255) comment '组织名称'")
    private String orgName;

    @ApiModelProperty("职位编码")
    @Column(name = "position_code", columnDefinition = "varchar(255) comment '职位编码'")
    private String positionCode;

    @ApiModelProperty("关联统筹方案编码")
    @Column(name = "release_code", columnDefinition = "varchar(255) comment '关联统筹方案编码'")
    private String releaseCode;

    @ApiModelProperty("关联统筹方案名称")
    @Column(name = "release_name", columnDefinition = "varchar(255) comment '关联统筹方案名称'")
    private String releaseName;
}