package com.biz.crm.tpm.admin.web.bireport.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.List;
/**
 * <AUTHOR>
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_bi_marketing_plan_case_report")
@Table(name = "tpm_bi_marketing_plan_case_report")
@org.hibernate.annotations.Table(appliesTo = "tpm_bi_marketing_plan_case_report", comment = "BI活动明细报表")
@ApiModel(value = "BiMarketingPlanCaseReportEntity", description = "BI活动明细报表")
public class BiMarketingPlanCaseReportEntity  extends TenantFlagOpEntity {
    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", columnDefinition = "varchar(255) comment '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("完全兑付日期")
    @Column(name = "whole_cash_date", columnDefinition = "varchar(255) comment '完全兑付日期'")
    private String wholeCashDate;


    @ApiModelProperty("关联指引编码")
    @Column(name = "release_code", columnDefinition = "varchar(255) comment '关联指引编码'")
    private String releaseCode;

    @ApiModelProperty("关联指引名称")
    @Column(name = "release_name", columnDefinition = "varchar(255) comment '关联指引名称'")
    private String releaseName;


    @ApiModelProperty("方案规划编码")
    @Column(name = "scheme_code", columnDefinition = "varchar(255) comment '方案规划编码'")
    private String schemeCode;


    @ApiModelProperty("方案规划编码")
    @Column(name = "head_scheme_code", columnDefinition = "varchar(255) comment '关联总部方案编码'")
    private String headSchemeCode;

    @ApiModelProperty("关联总部方案名称")
    @Column(name = "head_scheme_name", columnDefinition = "varchar(255) comment '关联总部方案名称'")
    private String headSchemeName;

    @ApiModelProperty("方案规划名称")
    @Column(name = "scheme_name", columnDefinition = "varchar(255) comment '方案规划名称'")
    private String schemeName;

    @ApiModelProperty("方案类型")
    @Column(name = "scheme_type", columnDefinition = "varchar(255) comment '方案类型'")
    private String schemeType;


    @ApiModelProperty("活动开始时间")
    @Column(name = "start_date", columnDefinition = "varchar(255) comment '活动开始时间'")
    private String startDate;

    @ApiModelProperty("活动结束时间")
    @Column(name = "end_date", columnDefinition = "varchar(255) comment '活动结束时间'")
    private String endDate;

    @ApiModelProperty("公司代码")
    @Column(name = "company_code", columnDefinition = "varchar(255) comment '公司代码'")
    private String companyCode;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", columnDefinition = "varchar(255) comment '客户编码'")
    private String customerCode;


    @ApiModelProperty("合作类型")
    @Column(name = "hzlx", columnDefinition = "varchar(255) comment '客户编码'")
    private String hzlx;


    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", columnDefinition = "varchar(255) comment '客户名称'")
    private String customerName;


    @ApiModelProperty("门店编码")
    @Column(name = "terminal_code", columnDefinition = "varchar(255) comment '门店编码'")
    private String terminalCode;


    @ApiModelProperty("门店名称")
    @Column(name = "terminal_name", columnDefinition = "varchar(255) comment '门店名称'")
    private String terminalName;


    @ApiModelProperty("兑付方式")
    @Column(name = "cash_type", columnDefinition = "varchar(255) comment '兑付方式'")
    private String cashType;

    @ApiModelProperty("申请人账号")
    @Column(name = "create_account", columnDefinition = "varchar(255) comment '申请人账号'")
    private String createAccount;

    @ApiModelProperty("申请人姓名")
    @Column(name = "create_name", columnDefinition = "varchar(255) comment '申请人姓名'")
    private String createName;


    @ApiModelProperty("预算编码")
    @Column(name = "budget_code", columnDefinition = "varchar(255) comment '预算编码'")
    private String budgetCode;

    @ApiModelProperty("预算年月")
    @Column(name = "years", columnDefinition = "varchar(255) comment '预算年月'")
    private String years;


    @ApiModelProperty("预算科目")
    @Column(name = "budget_subject_name", columnDefinition = "varchar(255) comment '预算科目'")
    private String budgetSubjectName;

    @ApiModelProperty("费用项目名称")
    @Column(name = "detail_name", columnDefinition = "varchar(255) comment '费用项目名称'")
    private String detailName;

    @ApiModelProperty("费用使用部门编码")
    @Column(name = "belong_department_code", columnDefinition = "varchar(255) comment '费用使用部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("一级部门")
    @Column(name = "region_name", columnDefinition = "varchar(255) comment '一级部门'")
    private String regionName;


    @ApiModelProperty("费用使用部门")
    @Column(name = "belong_department_name", columnDefinition = "varchar(255) comment '费用使用部门'")
    private String belongDepartmentName;


    @ApiModelProperty("费用承担部门编码")
    @Column(name = "bear_department_code", columnDefinition = "varchar(255) comment '费用承担部门编码'")
    private String bearDepartmentCode;


    @ApiModelProperty("费用承担部门")
    @Column(name = "bear_department_name", columnDefinition = "varchar(255) comment '费用承担部门'")
    private String bearDepartmentName;



    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", columnDefinition = "varchar(255) comment '成本中心编码'")
    private String costCenterCode;



    @ApiModelProperty("成本中心")
    @Column(name = "cost_center_name", columnDefinition = "varchar(255) comment '成本中心'")
    private String costCenterName;


    @ApiModelProperty("结案状态")
    @Column(name = "audit_status", columnDefinition = "varchar(255) comment '结案状态'")
    private String auditStatus;



    @ApiModelProperty("兑付状态")
    @Column(name = "cash_status", columnDefinition = "varchar(255) comment '兑付状态'")
    private String cashStatus;





    @ApiModelProperty("申请金额")
    @Column(name = "apply_amount", columnDefinition = "varchar(255) comment '申请金额'")
    private String applyAmount;


    @ApiModelProperty("品项")
    @Column(name = "item_name", columnDefinition = "varchar(255) comment '品项'")
    private String itemName;


    @ApiModelProperty("计提金额")
    @Column(name = "withholding_amount", columnDefinition = "varchar(255) comment '计提金额'")
    private String withholdingAmount;


    @ApiModelProperty("结案金额")
    @Column(name = "audit_amount", columnDefinition = "varchar(255) comment '结案金额'")
    private String auditAmount;


    @ApiModelProperty("兑付金额")
    @Column(name = "cash_amount", columnDefinition = "varchar(255) comment '兑付金额'")
    private String cashAmount;


    @ApiModelProperty("预付可冲销金额")
    @Column(name = "available_reversed_amount", columnDefinition = "varchar(255) comment '预付可冲销金额'")
    private String availableReversedAmount;




    @ApiModelProperty("实际支付金额")
    @Column(name = "prepay_amount", columnDefinition = "varchar(255) comment '实际支付金额'")
    private String prepayAmount;



    @ApiModelProperty("推送DMS状态")
    @Column(name = "push_status", columnDefinition = "varchar(255) comment '推送DMS状态'")
    private String pushStatus;



    @ApiModelProperty("推送错误描述")
    @Column(name = "push_msg", columnDefinition = "varchar(255) comment '推送错误描述0'")
    private String pushMsg;



    @ApiModelProperty("多部门标记")
    @Column(name = "much_department_mark", columnDefinition = "varchar(255) comment '多部门标记'")
    private String muchDepartmentMark;



    @ApiModelProperty("活动执行编码")
    @Column(name = "act_execute_code", columnDefinition = "varchar(255) comment '活动执行编码'")
    private String actExecuteCode;



    @ApiModelProperty("DMS单据号")
    @Column(name = "dms_code", columnDefinition = "varchar(255) comment 'DMS单据号'")
    private String dmsCode;


    @ApiModelProperty("是否计提")
    @Column(name = "with_holding_status", columnDefinition = "varchar(255) comment '是否计提'")
    private String withHoldingStatus;


    @ApiModelProperty("活动描述")
    @Column(name = "act_desc", columnDefinition = "varchar(1000) comment '活动描述'")
    private String actDesc;


    @ApiModelProperty("方案规划明细类型")
    @Column(name = "case_type", columnDefinition = "varchar(255) comment '方案规划明细类型'")
    private String caseType;


    @ApiModelProperty("剩余可结案金额")
    @Column(name = "rest_available_audit_amount", columnDefinition = "varchar(255) comment '剩余可结案金额'")
    private String restAvailableAuditAmount;

    @ApiModelProperty("管报计提金额")
    @Column(name = "with_holding_report_amount", columnDefinition = "varchar(255) comment '管报计提金额'")
    private String withHoldingReportAmount;

    @ApiModelProperty("已预付金额")
    @Column(name = "already_prepaid_amount", columnDefinition = "varchar(255) comment '已预付金额'")
    private String alreadyPrepaidAmount;

    @ApiModelProperty("剩余可兑付金额")
    @Column(name = "rest_available_cash_amount", columnDefinition = "varchar(255) comment '剩余可兑付金额'")
    private String restAvailableCashAmount;

    @ApiModelProperty("管报实际总金额")
    @Column(name = "actual_report_total_amount", columnDefinition = "varchar(255) comment '管报实际总金额'")
    private String actualReportTotalAmount;

    @ApiModelProperty("陈列卡板数")
    @Column(name = "display_card_num", columnDefinition = "varchar(255) comment '陈列卡板数'")
    private String displayCardNum;


    @ApiModelProperty("政策形式名称")
    @Column(name = "condition_formula_name", columnDefinition = "varchar(255) comment '政策形式名称'")
    private String conditionFormulaName;

    @ApiModelProperty("达成条件")
    @Column(name = "condition_num", columnDefinition = "varchar(255) comment '达成条件'")
    private String conditionNum;

    @ApiModelProperty("搭赠/优惠数量")
    @Column(name = "give_num", columnDefinition = "varchar(255) comment '搭赠/优惠数量'")
    private String giveNum;

    @ApiModelProperty("本品小类/名称")
    @Column(name = "level_or_product_name", columnDefinition = "varchar(255) comment '本品小类/名称'")
    private String levelOrProductName;


    @ApiModelProperty("返利品项/产品名称")
    @Column(name = "fee_product_or_item_name", columnDefinition = "varchar(255) comment '返利品项/产品名称'")
    private String feeProductOrItemName;

}
