package com.biz.crm.tpm.business.pay.local.service.internal;

import com.biz.crm.business.common.page.cache.service.internal.BusinessPageCacheServiceImpl;
import com.biz.crm.business.common.sdk.enums.BooleanEnum;
import com.biz.crm.mdm.business.dictionary.sdk.service.DictDataVoService;
import com.biz.crm.mdm.business.dictionary.sdk.vo.DictDataVo;
import com.biz.crm.tpm.business.pay.local.repository.FeeCashDetailRepository;
import com.biz.crm.tpm.business.pay.sdk.constant.FeeCashConstant;
import com.biz.crm.tpm.business.pay.sdk.dto.FeeCashDetailDto;
import com.biz.crm.tpm.business.pay.sdk.service.FeeCashDetailService;
import com.biz.crm.tpm.business.pay.sdk.vo.FeeCashDetailVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FeeCashDetailServiceImpl extends BusinessPageCacheServiceImpl<FeeCashDetailVo, FeeCashDetailDto> implements FeeCashDetailService {

    @Autowired(required = false)
    private DictDataVoService dictDataVoService;
    @Autowired(required = false)
    private FeeCashDetailRepository feeCashDetailRepository;

    /**
     * 是否展示TOC
     *
     * @param cacheKeyDetail
     * @param payeeErpCode
     * @return
     */
    @Override
    public String showToc(String cacheKeyDetail, String payeeErpCode) {
        List<FeeCashDetailDto> cacheList = findCacheList(cacheKeyDetail);
        return showToc(cacheList, payeeErpCode);
    }

    /**
     * 是否展示TOC
     *
     * @param cacheList
     * @param payeeErpCode
     * @return
     */
    @Override
    public String showToc(List<FeeCashDetailDto> cacheList, String payeeErpCode) {
        String beShow = BooleanEnum.FALSE.getCapital();

        if (!CollectionUtils.isEmpty(cacheList)) {
            List<DictDataVo> dictDataVos = dictDataVoService.findByDictTypeCode(FeeCashConstant.TOC_PAYMENT);
            Map<String, Map<String, DictDataVo>> dictMap = dictDataVos.stream().collect(Collectors.groupingBy(e -> e.getExt1(),
                    Collectors.toMap(e -> e.getExt2(), Function.identity(), (a, b) -> a)));
            for (FeeCashDetailDto detail : cacheList) {
                if (dictMap.containsKey(detail.getBudgetSubjectCode())) {
                    Map<String, DictDataVo> dataVoMap = dictMap.get(detail.getBudgetSubjectCode());
                    if (dataVoMap.containsKey(payeeErpCode)) {
                        beShow = BooleanEnum.TRUE.getCapital();
                        break;
                    }
                }
            }
        }
        return beShow;
    }

    /**
     * 按客户+结案单号查询兑付单
     *
     * @param auditCodes
     * @param customerCodes
     * @return
     */
    @Override
    public List<FeeCashDetailVo> findByAuditCustomer(List<String> auditCodes, List<String> customerCodes) {
        return feeCashDetailRepository.findByAuditCustomer(auditCodes, customerCodes);
    }
}
