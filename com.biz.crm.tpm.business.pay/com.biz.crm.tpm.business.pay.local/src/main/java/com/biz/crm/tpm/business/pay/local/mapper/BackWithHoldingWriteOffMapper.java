package com.biz.crm.tpm.business.pay.local.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.biz.crm.tpm.business.pay.local.entity.BackWithHoldingWriteOff;
import com.biz.crm.tpm.business.pay.local.entity.WithHoldingWriteOff;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 费用反冲销(BackWithHoldingWriteOff)表mybatis访问层
 */
public interface BackWithHoldingWriteOffMapper extends BaseMapper<BackWithHoldingWriteOff> {
    List<BackWithHoldingWriteOff> findByUniqueKeys(@Param("uniqueKeys") List<String> uniqueKeys);


}
