package com.biz.crm.tpm.business.activities.giftrebatereport.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.biz.crm.business.common.local.entity.TenantFlagOpEntity;
import com.biz.crm.business.common.local.entity.UuidFlagOpEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 搭赠及返利实时金额占用报表实体类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("tpm_gift_rebate_occupy_amount_report")
@Table(name = "tpm_gift_rebate_occupy_amount_report")
@org.hibernate.annotations.Table(appliesTo = "tpm_gift_rebate_occupy_amount_report", comment = "搭赠及返利实时金额占用报表")
@ApiModel(value = "GiftAndRebateOccupyAmountReport", description = "搭赠及返利实时金额占用报表")
public class GiftAndRebateOccupyAmountReport extends TenantFlagOpEntity {

    @ApiModelProperty("方案编码")
    @Column(name = "scheme_code", length = 64, columnDefinition = "varchar(64) COMMENT '方案编码'")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    @Column(name = "scheme_name", length = 128, columnDefinition = "varchar(128) COMMENT '方案名称'")
    private String schemeName;

    @ApiModelProperty("方案类型")
    @Column(name = "scheme_type", length = 32, columnDefinition = "varchar(32) COMMENT '方案类型'")
    private String schemeType;

    @ApiModelProperty("方案明细编码")
    @Column(name = "scheme_detail_code", length = 64, columnDefinition = "varchar(64) COMMENT '方案明细编码'")
    private String schemeDetailCode;

    @ApiModelProperty("开始时间")
    @Column(name = "start_date", length = 20, columnDefinition = "varchar(20) COMMENT '开始时间'")
    private String startDate;

    @ApiModelProperty("结束时间")
    @Column(name = "end_date", length = 20, columnDefinition = "varchar(20) COMMENT '结束时间'")
    private String endDate;

    @ApiModelProperty("客户编码")
    @Column(name = "customer_code", length = 64, columnDefinition = "varchar(64) COMMENT '客户编码'")
    private String customerCode;

    @ApiModelProperty("客户名称")
    @Column(name = "customer_name", length = 128, columnDefinition = "varchar(128) COMMENT '客户名称'")
    private String customerName;

    @ApiModelProperty("使用部门编码")
    @Column(name = "belong_department_code", length = 64, columnDefinition = "varchar(64) COMMENT '使用部门编码'")
    private String belongDepartmentCode;

    @ApiModelProperty("使用部门名称")
    @Column(name = "belong_department_name", length = 128, columnDefinition = "varchar(128) COMMENT '使用部门名称'")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    @Column(name = "bear_department_code", length = 64, columnDefinition = "varchar(64) COMMENT '承担部门编码'")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    @Column(name = "bear_department_name", length = 128, columnDefinition = "varchar(128) COMMENT '承担部门名称'")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    @Column(name = "cost_center_code", length = 64, columnDefinition = "varchar(64) COMMENT '成本中心编码'")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    @Column(name = "cost_center_name", length = 128, columnDefinition = "varchar(128) COMMENT '成本中心名称'")
    private String costCenterName;

    @ApiModelProperty("费用项目编码")
    @Column(name = "detail_code", length = 64, columnDefinition = "varchar(64) COMMENT '费用项目编码'")
    private String detailCode;

    @ApiModelProperty("费用项目名称")
    @Column(name = "detail_name", length = 128, columnDefinition = "varchar(128) COMMENT '费用项目名称'")
    private String detailName;

    @ApiModelProperty("品项编码")
    @Column(name = "item_code", length = 64, columnDefinition = "varchar(64) COMMENT '品项编码'")
    private String itemCode;

    @ApiModelProperty("品项名称")
    @Column(name = "item_name", length = 128, columnDefinition = "varchar(128) COMMENT '品项名称'")
    private String itemName;

    @ApiModelProperty("本品数量")
    @Column(name = "condition_num", length = 32, columnDefinition = "varchar(32) COMMENT '本品数量'")
    private String conditionNum;

    @ApiModelProperty("赠品数量")
    @Column(name = "give_num", length = 32, columnDefinition = "varchar(32) COMMENT '赠品数量'")
    private String giveNum;

    @ApiModelProperty("本品小类名称")
    @Column(name = "bp_level_name", length = 128, columnDefinition = "varchar(128) COMMENT '本品小类名称'")
    private String bpLevelName;

    @ApiModelProperty("本品小类编码")
    @Column(name = "bp_level_code", length = 64, columnDefinition = "varchar(64) COMMENT '本品小类编码'")
    private String bpLevelCode;

    @ApiModelProperty("规划金额")
    @Column(name = "plan_amount", precision = 18, scale = 2, columnDefinition = "decimal(18,2) COMMENT '规划金额'")
    private BigDecimal planAmount;

    @ApiModelProperty("占用金额")
    @Column(name = "occupy_amount", precision = 18, scale = 2, columnDefinition = "decimal(18,2) COMMENT '占用金额'")
    private BigDecimal occupyAmount;

    @ApiModelProperty("返利品项或产品名称")
    @Column(name = "fl_item_or_product_name", length = 128, columnDefinition = "varchar(128) COMMENT '返利品项或产品名称'")
    private String flItemOrProductName;

    @ApiModelProperty("返利品项或产品编码")
    @Column(name = "fl_item_or_product_code", length = 64, columnDefinition = "varchar(64) COMMENT '返利品项或产品编码'")
    private String flItemOrProductCode;

    @ApiModelProperty("返利政策")
    @Column(name = "rebate_policy", length = 256, columnDefinition = "varchar(256) COMMENT '返利政策'")
    private String rebatePolicy;

    @ApiModelProperty("达成条件")
    @Column(name = "condition_formula", length = 512, columnDefinition = "varchar(512) COMMENT '达成条件'")
    private String conditionFormula;

    @ApiModelProperty("返利标准")
    @Column(name = "rebate_standard", length = 256, columnDefinition = "varchar(256) COMMENT '返利标准'")
    private String rebateStandard;
}
