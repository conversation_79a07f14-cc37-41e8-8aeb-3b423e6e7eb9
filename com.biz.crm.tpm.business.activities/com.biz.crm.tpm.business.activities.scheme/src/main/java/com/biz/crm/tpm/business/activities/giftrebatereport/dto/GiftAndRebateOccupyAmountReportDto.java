package com.biz.crm.tpm.business.activities.giftrebatereport.dto;

import com.biz.crm.business.common.sdk.dto.TenantFlagOpDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 搭赠及返利实时金额占用报表查询DTO
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "GiftAndRebateOccupyAmountReportDto", description = "搭赠及返利实时金额占用报表查询DTO")
public class GiftAndRebateOccupyAmountReportDto extends TenantFlagOpDto {

    @ApiModelProperty("方案编码")
    private String schemeCode;

    @ApiModelProperty("方案名称")
    private String schemeName;

    @ApiModelProperty("方案类型")
    private String schemeType;

    @ApiModelProperty("方案明细编码")
    private String schemeDetailCode;

    @ApiModelProperty("开始时间-起")
    private String startDateBegin;

    @ApiModelProperty("开始时间-止")
    private String startDateEnd;

    @ApiModelProperty("结束时间-起")
    private String endDateBegin;

    @ApiModelProperty("结束时间-止")
    private String endDateEnd;

    @ApiModelProperty("客户编码")
    private String customerCode;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("使用部门编码")
    private String belongDepartmentCode;

    @ApiModelProperty("使用部门名称")
    private String belongDepartmentName;

    @ApiModelProperty("承担部门编码")
    private String bearDepartmentCode;

    @ApiModelProperty("承担部门名称")
    private String bearDepartmentName;

    @ApiModelProperty("成本中心编码")
    private String costCenterCode;

    @ApiModelProperty("成本中心名称")
    private String costCenterName;

    @ApiModelProperty("费用项目编码")
    private String detailCode;

    @ApiModelProperty("费用项目名称")
    private String detailName;

    @ApiModelProperty("品项编码")
    private String itemCode;

    @ApiModelProperty("品项名称")
    private String itemName;

    @ApiModelProperty("本品小类编码")
    private String bpLevelCode;

    @ApiModelProperty("本品小类名称")
    private String bpLevelName;

    @ApiModelProperty("规划金额-最小值")
    private BigDecimal planAmountMin;

    @ApiModelProperty("规划金额-最大值")
    private BigDecimal planAmountMax;

    @ApiModelProperty("占用金额-最小值")
    private BigDecimal occupyAmountMin;

    @ApiModelProperty("占用金额-最大值")
    private BigDecimal occupyAmountMax;

    @ApiModelProperty("返利品项或产品编码")
    private String flItemOrProductCode;

    @ApiModelProperty("返利品项或产品名称")
    private String flItemOrProductName;

    @ApiModelProperty("返利政策")
    private String rebatePolicy;

    @ApiModelProperty("排序字段")
    private String orderBy;

    @ApiModelProperty("排序方向")
    private String orderDirection;
}
